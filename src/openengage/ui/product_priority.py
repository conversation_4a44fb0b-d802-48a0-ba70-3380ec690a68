"""
Product priority UI component for OpenEngage.
"""
import json
import streamlit as st
import pandas as pd
import os
import numpy as np
from utils.file_utils import get_all_products, load_user_journey
import openai

def display_product_priority():
    """Display the product priority management interface"""
    st.markdown("""
        <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;'>
            <h2 style='color: #FFFFFF; margin: 0; font-size: 1.2rem;'>Product Priority Management</h2>
            <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                Set priorities for your organization's products.
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)
    elif hasattr(st.session_state, 'organization_url'):
        org_url = st.session_state.organization_url

    if not org_url:
        st.error("Organization URL not found. Please complete organization setup first.")
        return

    # Load products with optional filtering
    try:
        # Get all products
        all_products = get_all_products()

        # Filter products by organization URL
        if org_filter_enabled:
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                     p.get("organization_url", "") == org_url]
        else:
            # If filter is disabled, still filter by organization URL for this specific module
            # as it's meant to manage priorities for a specific organization
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                     p.get("organization_url", "") == org_url]

        if not org_products:
            st.info("No products found for your organization. Please add products first.")
            return

        # Add default priority if not already present
        for i, product in enumerate(org_products, 1):
            if "Priority" not in product:
                product["Priority"] = i

        # Check if any product has a cross-sell stage in its user journey
        has_cross_sell = False
        for product in org_products:
            product_name = product.get("Product_Name", "")
            if product_name:
                # Load user journey for the product
                user_journey = load_user_journey(product_name)
                # Check if any stage is marked as cross-sell stage
                if any(stage.get('is_cross_sell_stage', False) for stage in user_journey):
                    has_cross_sell = True
                    break
        
        # Sort products by priority
        org_products.sort(key=lambda x: x.get("Priority", 999))
        
        # Create DataFrame for display
        product_data = []
        for i, product in enumerate(org_products, 1):
            product_dict = {
                "S. No.": i,
                "Name of Product": product.get("Product_Name", "Unknown"),
                "URL of Product": product.get("Product_URL", "Unknown"),
                "Priority": product.get("Priority", i)
            }
            
            # Add Cross Sell Product column if cross-sell exists in any product
            if has_cross_sell:
                # Check if there's already a Cross_Sell_Product specified in the product data
                if "Cross_Sell_Product" in product and product["Cross_Sell_Product"]:
                    # Use the existing cross-sell product
                    product_dict["Cross Sell Product"] = product["Cross_Sell_Product"]
                else:
                    # Find the most similar product for cross-selling using embeddings
                    try:
                        # Get current product details for embedding
                        current_product_name = product.get("Product_Name", "")
                        current_product_summary = product.get("Product_Summary", "")
                        current_product_features = product.get("Product_Features", [])
                        current_product_type = product.get("Type_of_Product", "")
                        
                        # Create a text representation for embedding
                        current_product_text = f"Product: {current_product_name}. Type: {current_product_type}. Summary: {current_product_summary}. Features: {', '.join(current_product_features)}".strip()
                        
                        # Get other products to compare with
                        other_products = [p for p in org_products if p.get("Product_Name", "") != current_product_name]
                        
                        if other_products:
                            # Generate embeddings for all products
                            product_embeddings = {}
                            
                            # Generate embedding for current product
                            try:
                                # Check if OpenAI API key is available
                                if 'OPENAI_API_KEY' in os.environ or hasattr(st.session_state, 'openai_api_key'):
                                    # Use API key from environment or session state
                                    api_key = os.environ.get('OPENAI_API_KEY', 
                                                          getattr(st.session_state, 'openai_api_key', None))
                                    
                                    if api_key:
                                        openai.api_key = api_key
                                        
                                        # Get embedding for current product
                                        response = openai.embeddings.create(
                                            model="text-embedding-3-small",
                                            input=current_product_text
                                        )
                                        current_embedding = response.data[0].embedding
                                        
                                        # Generate embeddings for other products
                                        similarities = []
                                        for other_product in other_products:
                                            other_name = other_product.get("Product_Name", "")
                                            other_summary = other_product.get("Product_Summary", "")
                                            other_features = other_product.get("Product_Features", [])
                                            other_type = other_product.get("Type_of_Product", "")
                                            
                                            other_text = f"Product: {other_name}. Type: {other_type}. Summary: {other_summary}. Features: {', '.join(other_features)}".strip()
                                            
                                            # Get embedding for other product
                                            other_response = openai.embeddings.create(
                                                model="text-embedding-3-small",
                                                input=other_text
                                            )
                                            other_embedding = other_response.data[0].embedding
                                            
                                            # Calculate cosine similarity
                                            similarity = np.dot(current_embedding, other_embedding) / (np.linalg.norm(current_embedding) * np.linalg.norm(other_embedding))
                                            
                                            similarities.append({
                                                "product": other_product,
                                                "similarity": similarity
                                            })
                                        
                                        if similarities:
                                            # Sort by similarity (highest first)
                                            similarities.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                                            most_similar = similarities[0]["product"]
                                            product_dict["Cross Sell Product"] = most_similar.get("Product_Name", "None")
                                            
                                            # Store the similarity score for reference
                                            if "Cross_Sell_Similarity" not in product:
                                                product["Cross_Sell_Similarity"] = float(similarities[0]["similarity"])
                                        else:
                                            product_dict["Cross Sell Product"] = "None"
                                    else:
                                        st.warning("OpenAI API key not found. Using priority-based cross-sell instead.", icon="⚠️")
                                        # Fallback to priority-based cross-sell
                                        current_priority = product.get("Priority", i)
                                        next_products = [p for p in org_products if p.get("Priority", 999) > current_priority]
                                        if next_products:
                                            next_products.sort(key=lambda x: x.get("Priority", 999))
                                            next_product = next_products[0]
                                            product_dict["Cross Sell Product"] = next_product.get("Product_Name", "None")
                                        else:
                                            product_dict["Cross Sell Product"] = "None"
                                else:
                                    st.warning("OpenAI API key not found. Using priority-based cross-sell instead.", icon="⚠️")
                                    # Fallback to priority-based cross-sell
                                    current_priority = product.get("Priority", i)
                                    next_products = [p for p in org_products if p.get("Priority", 999) > current_priority]
                                    if next_products:
                                        next_products.sort(key=lambda x: x.get("Priority", 999))
                                        next_product = next_products[0]
                                        product_dict["Cross Sell Product"] = next_product.get("Product_Name", "None")
                                    else:
                                        product_dict["Cross Sell Product"] = "None"
                            except Exception as e:
                                st.warning(f"Error generating embeddings: {str(e)}. Using priority-based cross-sell instead.", icon="⚠️")
                                # Fallback to priority-based cross-sell
                                current_priority = product.get("Priority", i)
                                next_products = [p for p in org_products if p.get("Priority", 999) > current_priority]
                                if next_products:
                                    next_products.sort(key=lambda x: x.get("Priority", 999))
                                    next_product = next_products[0]
                                    product_dict["Cross Sell Product"] = next_product.get("Product_Name", "None")
                                else:
                                    product_dict["Cross Sell Product"] = "None"
                        else:
                            # No other products available for cross-sell
                            product_dict["Cross Sell Product"] = "None"
                    except Exception as e:
                        st.error(f"Error processing cross-sell products: {str(e)}")
                        # Fallback to no cross-sell
                        product_dict["Cross Sell Product"] = "None"
            
            product_data.append(product_dict)

        # Create a data editor for priorities
        st.write("### Product Priorities")
        st.write("Review and adjust the priority of your products:")

        # Create DataFrame with priority columns
        df = pd.DataFrame(product_data)
        
        # Build column configuration
        column_config = {
            "S. No.": st.column_config.NumberColumn(
                "S. No.",
                help="Serial number",
                disabled=True,
                width="small"
            ),
            "Name of Product": st.column_config.TextColumn(
                "Name of Product",
                help="Product name",
                disabled=True,
                width="medium"
            ),
            "URL of Product": st.column_config.TextColumn(
                "URL of Product",
                help="Product URL",
                disabled=True,
                width="medium"
            ),
            "Priority": st.column_config.NumberColumn(
                "Priority",
                help="Set priority (lower numbers = higher priority)",
                min_value=1,
                max_value=len(org_products),
                step=1,
                width="small"
            )
        }
        
        # Add Cross Sell Product column config if it exists in the dataframe
        if "Cross Sell Product" in df.columns:
            # Get product names for dropdown options (plus None option)
            product_names = [p.get("Product_Name", "") for p in org_products]
            product_options = ["None"] + [name for name in product_names if name]
            
            column_config["Cross Sell Product"] = st.column_config.SelectboxColumn(
                "Cross Sell Product",
                help="Select product for cross-selling",
                options=product_options,
                width="medium"
            )
        
        edited_df = st.data_editor(
            df,
            column_config=column_config,
            hide_index=True,
            key="priority_editor"
        )

        # Save button
        if st.button("Save Product Priorities", type="primary"):
            # Get the edited priorities and cross-sell products if available
            edited_priorities = edited_df["Priority"].tolist()
            has_cross_sell_column = "Cross Sell Product" in edited_df.columns
            
            if has_cross_sell_column:
                cross_sell_products = edited_df["Cross Sell Product"].tolist()
            
            # Update priorities and cross sell products in org_products
            for i, product in enumerate(org_products):
                # Update priority
                product["Priority"] = int(edited_priorities[i])
                
                # Update cross sell product if available
                if has_cross_sell_column:
                    cross_sell_product = cross_sell_products[i]
                    if cross_sell_product != "None":
                        product["Cross_Sell_Product"] = cross_sell_product
                        # Clear any existing similarity score when manually setting cross-sell product
                        if "Cross_Sell_Similarity" in product:
                            product.pop("Cross_Sell_Similarity")
                    elif "Cross_Sell_Product" in product:
                        # Remove cross sell product if set to None
                        product.pop("Cross_Sell_Product")
                        # Also remove similarity score
                        if "Cross_Sell_Similarity" in product:
                            product.pop("Cross_Sell_Similarity")

            # Update product details in main list
            for org_product in org_products:
                for i, product in enumerate(all_products):
                    if product.get("Product_Name") == org_product.get("Product_Name"):
                        all_products[i] = org_product
                        break

            # Save to file
            with open("data/product_details.json", "w") as f:
                json.dump(all_products, f, indent=4)

            # Show success message
            success_placeholder = st.empty()
            success_placeholder.success("✅ Product priorities saved successfully! Redirecting to Templates Generator...")

            # Set up redirection after 3 seconds
            import time
            time.sleep(3)

            # Reset current view and set templates generator view
            st.session_state.show_product_priority = False
            st.session_state.show_templates_generator = True
            st.rerun()

    except (FileNotFoundError, json.JSONDecodeError) as e:
        st.error(f"Error loading product data: {str(e)}")
